name: "Build & push devcontainer"

on:
  push:
    branches:
      - master
    paths:
      - .devcontainer/**
      - .github/workflows/build_images.yml
  pull_request:
    paths:
      - .devcontainer/**
      - .github/workflows/build_images.yml

permissions:
  contents: read
  packages: write # Required for GHCR

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        ruby_image:
          - "ruby:2.7.8-slim-bullseye"
          - "ruby:3.0.7-slim-bullseye"
          - "ruby:3.1.7-slim-bookworm"
          - "ruby:3.2.9-slim-bookworm"
          - "ruby:3.3.9-slim-bookworm"
          - "ruby:3.4.5-slim-bookworm"
          - "jruby:9.4.13.0"
          - "jruby:latest"

    steps:
      - name: Check out current commit
        uses: actions/checkout@v4

      - name: Build and push devcontainer image
        id: build
        uses: getsentry/action-build-and-push-images@main
        with:
          image_name: 'sentry-ruby-devcontainer-${{ matrix.ruby_image }}'
          dockerfile_path: '.devcontainer/Dockerfile'
          ghcr: true
          publish_on_pr: true

      - name: Use outputs
        run: |
          echo "GHCR URL: ${{ steps.build.outputs.ghcr_image_url }}"
